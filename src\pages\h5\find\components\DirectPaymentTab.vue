<template>
  <div>
    <div
      class="flex justify-between items-center py-[18px] px-[12px] border-b-1 border-[#F2F2F2]"
    >
      <div
        class="text-[#1EA62A] text-[18px] leading-[18px] font-medium"
        :class="props.showTabs ? 'text-[#1EA62A]' : 'text-[#333]'"
      >
        {{ authStore.i18n("cm_find.directOrderList") }}
        <span class="text-[14px] leading-[14px] ml-[4px]">
          ({{ cartData.stat?.skuCount || 0 }})
        </span>
      </div>
      <div class="flex items-center">
        <img
          loading="lazy"
          alt="back"
          class="w-[0.24rem] mr-[0.08rem]"
          src="@/assets/icons/common/address.svg"
          referrerpolicy="no-referrer"
        />
        <mobile-country-select
          @save="onSaveCountry"
          spm="select_site_from_cart"
        />
      </div>
    </div>

    <!-- 地址选择区域 -->
    <div class="bg-white pt-[12px] pl-[9px] pr-[16px]">
      <div class="flex items-center">
        <icon-card name="mdi:address-marker" size="24" color="#e50113">
        </icon-card>
        <div class="text-[0.32rem] leading-[0.32rem]">
          {{ authStore.i18n("cm_addr.shippingAddress") }}
        </div>
        <span
          class="text-[#e50113] ml-[2px]"
          v-if="isEmptyObject(pageData?.orderAddress)"
          >*</span
        >
      </div>
      <!-- 已选择地址 -->
      <div
        v-if="!isEmptyObject(pageData?.orderAddress)"
        class="border-b-1 border-[#F2F2F2] flex items-start ml-[25px] pb-[14px]"
        @click="onShowSelectAddress"
      >
        <div class="flex-1">
          <div class="text-[0.28rem] font-medium mb-[0.08rem]">
            {{ pageData?.orderAddress.contactName }}
            {{ formatPhone(pageData?.orderAddress) }}
          </div>
          <div class="text-[0.28rem] text-[#666] leading-[0.32rem]">
            {{ pageData?.orderAddress.fullAddress }}
          </div>
        </div>
        <img
          alt="edit"
          @click="onShowSelectAddress"
          class="w-[16px] ml-[22px] cursor-pointer"
          src="@/assets/icons/find/edit.svg"
        />
      </div>

      <!-- 未选择地址 -->
      <div
        v-else
        @click="onOpenAddAddr"
        class="border border-dashed border-[#E50113] rounded-[0.12rem] py-[0.24rem] h-[40px] text-center flex items-center justify-center gap-[4px] mt-[4px] ml-[20px] mr-[14px]"
      >
        <img
          class="w-[16px] leading-[16px]"
          src="@/assets/icons/common/add-primary.svg"
          :alt="authStore.i18n('cm_addr.shippingAddress')"
        />
        <div class="text-[0.32rem] leading-[0.32rem] text-[#E50113]">
          {{ authStore.i18n("cm_addr.createNewAddress") }}
        </div>
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div
      class="goods-section bg-white rounded-[0.16rem] p-[0.16rem] mb-[0.16rem]"
    >
      <div v-if="cartData.goodsList.length">
        <!-- 二级 -->
        <div
          v-for="(goods, index) in cartData.goodsList"
          :key="goods.goodsId"
          class="mb-[0.5rem]"
        >
          <div class="flex items-center w-full">
            <n-checkbox
              class="mr-[0.16rem]"
              v-model:checked="goods.selected"
              @update:checked="
                (value) => $emit('onGoodsSelection', value, goods)
              "
            >
            </n-checkbox>
            <a
              :href="`/h5/goods/${goods.goodsId}${
                goods.padc ? `?padc=${goods.padc}` : ''
              }`"
              data-spm-box="cart-goods-list"
              :data-spm-index="index + 1"
            >
              <mobile-goods-card
                :goods="goods"
                class="flex-1"
                spmCode="cart-goods-list"
                ><icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="0.4rem"
                  class="mx-[0.16rem]"
                  @click.stop.prevent="$emit('onDeleteGoods', goods)"
                ></icon-card
              ></mobile-goods-card>
            </a>
          </div>
          <!-- 三级分类 -->
          <div
            v-for="sku in goods.skuList"
            :key="sku.skuId"
            class="mb-[0.24rem]"
          >
            <div class="flex items-center">
              <n-checkbox
                class="mr-[0.16rem]"
                v-model:checked="sku.selected"
                @update:checked="
                  (value) => $emit('onSkuSelection', value, sku, goods)
                "
              >
              </n-checkbox>
              <mobile-sku-card
                :sku="sku"
                :goods="goods"
                @onCartQtyUpdate="
                  (value) => $emit('onCartQtyUpdate', value, sku, goods)
                "
                :step="sku.minIncreaseQuantity"
                ><template v-slot:spec>
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="0.45rem"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  ></icon-card>
                </template>
                <template v-slot:delete>
                  <icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="0.4rem"
                    @click="$emit('onDeleteSku', sku, goods)"
                  >
                  </icon-card>
                </template>
              </mobile-sku-card>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-[0.8rem]">
        <icon-card
          name="material-symbols:inbox-outline"
          color="#999"
          size="1.2rem"
          class="mx-auto mb-[0.16rem]"
        />
        <div class="text-[0.28rem] text-[#999]">
          {{ authStore.i18n("cm_find.noData") }}
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div
      class="w-full fixed bottom-[1.32rem] bg-white pt-[0.24rem] pb-[0.32rem] px-[0.16rem]"
    >
      <div>
        <div class="flex items-start justify-between">
          <!-- 一级 -->
          <n-checkbox
            v-model:checked="cartData.selectAll"
            @update:checked="onAllSelection"
            ><span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_find_selectAll")
            }}</span></n-checkbox
          >
          <div class="flex gap-[0.08rem] items-center">
            <span class="text-[0.28rem] leading-[0.28rem]">
              {{ authStore.i18n("cm_find_itemsCost") }}:
            </span>
            <div
              class="text-[0.36rem] leading-[0.36rem] font-medium flex-shrink-0 flex-1"
              v-if="
                cartData.stat.selectTotalSalePrice ||
                cartData.stat.selectTotalSalePrice === 0
              "
            >
              <span
                class="text-[0.32rem] leading-[0.32rem] font-medium mr-[0.04rem]"
              >
                {{ monetaryUnit }} </span
              >{{ setNewUnit(cartData.stat.selectTotalSalePrice, true) }}
            </div>
            <icon-card
              color="#555"
              name="ri:arrow-down-s-line"
              size="26"
              @click="openInquireTip"
              class="hidden"
            />
          </div>
        </div>
        <div
          class="flex text-[0.28rem] leading-[0.28rem] items-center justify-end mt-[0.12rem] text-[#7F7F7F]"
        >
          <n-popover trigger="hover" raw>
            <template #trigger>
              <div
                class="cursor-pointer flex-shrink-0 mr-[0.06rem] text-[#7F7F7F] flex items-center"
              >
                <img
                  class="w-[0.28rem] h-[0.28rem] mr-[0.04rem]"
                  src="@/assets/icons/common/alert-circle.svg"
                  :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                  referrerpolicy="no-referrer"
                />
                {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
              </div>
            </template>
            <div
              style="
                z-index: 1;
                width: 300px;
                padding: 6px 14px;
                background-color: #fff4d4;
                transform-origin: inherit;
                border: 1px solid #f7ba2a;
              "
            >
              {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
            </div>
          </n-popover>

          <span class="flex-shrink-0" v-if="cartData?.totalEstimateFreight">{{
            setUnit(cartData.totalEstimateFreight)
          }}</span>
          <span v-else class="flex-shrink-0">{{
            authStore.i18n("cm_goods.pendingConfirmation")
          }}</span>
        </div>
      </div>
      <n-button
        block
        size="large"
        color="#E50113"
        text-color="#fff"
        class="rounded-[0.16rem] h-[0.92rem] mt-[0.32rem]"
        @click="onCheckout"
        data-spm-box="cart-to-checkout"
      >
        <div>
          <div class="text-[0.32rem] leading-[0.32rem] font-medium">
            {{ authStore.i18n("cm_find.inquireNow") }}
          </div>
          <div class="text-[0.28rem] leading-[0.28rem] mt-[0.04rem]">
            {{ authStore.i18n("cm_find_confirmWithoutPay") }}
          </div>
        </div>
      </n-button>
    </div>

    <!-- 地址选择抽屉 -->
    <SelectAddressDrawer
      ref="selectAddressDrawerRef"
      :addressList="pageData.addressList"
      @addAddress="onOpenAddAddr"
      @editAddress="onOpenEditAddr"
      @confirmAddress="onConfirmSelectAddress"
      @refresh="onUpdateListUserAddress"
    />

    <!-- 添加/编辑地址抽屉 -->
    <AddAddressDrawer
      ref="addAddressDrawerRef"
      :country-list="pageData.countryList"
      :country-regexes="pageData.countryRegexes"
      @onUpdateListUserAddress="onUpdateListUserAddress"
    />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import MobileGoodsCard from "./MobileGoodsCard.vue";
import MobileSkuCard from "./MobileSkuCard.vue";
import SelectAddressDrawer from "./SelectAddressDrawer.vue";
import AddAddressDrawer from "./AddAddressDrawer.vue";

const authStore = useAuthStore();
const config = useRuntimeConfig();
const selectAddressDrawerRef = ref<any>(null);
const addAddressDrawerRef = ref<any>(null);

const props = defineProps({
  cartData: {
    type: Object,
    default: () => {},
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
]);

// 响应式数据
const pageData = reactive<any>({
  showTipDrawer: false,
  orderAddress: <any>{},
  countryList: <any>[],
  addressList: <any>[],
  countryRegexes: <any>{},
});

onListUserAddress("init");
onGetCountry();

// 地址列表加载函数
async function onListUserAddress(type?: string) {
  const res: any = await useListUserAddress({});
  if (res?.result?.code === 200) {
    // 只保留秘鲁国家的地址
    const peruAddresses =
      res?.data?.filter(
        (item: any) => item.countryId === config.public.peruCountryId
      ) || [];

    pageData.addressList = peruAddresses;

    // 地址选择逻辑：优先选择默认地址，如果没有默认地址则选择第一条
    if (type === "init" && peruAddresses.length > 0) {
      // 查找默认地址
      const defaultAddress = peruAddresses.find((item: any) => item.isDefault);

      if (defaultAddress) {
        // 有默认地址，选择默认地址
        pageData.orderAddress = defaultAddress;
      } else {
        // 没有默认地址，选择第一条秘鲁地址
        pageData.orderAddress = peruAddresses[0];
      }
    }
  } else if (res?.result?.code === 403) {
    navigateToPage(
      `/h5/user/login`,
      { pageSource: window.location.href },
      false
    );
  }
}

// 获取国家列表
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    // 设置秘鲁国家的默认数据
    const country = pageData.countryList.find(
      (item: any) => item.id === config.public.peruCountryId
    );
    if (country) {
      pageData.countryRegexes = country;
    }
  }
}

const formatPhone = (address: any) => {
  return `${address.areaCode || ""} ${address.phone || ""}`.trim();
};

const onAllSelection = (value: boolean) => {
  emit("onAllSelection", value, true);
};

function onShowSelectAddress() {
  selectAddressDrawerRef.value?.onShowDrawer(pageData.orderAddress);
}

function onConfirmSelectAddress(address: any) {
  pageData.orderAddress = address;
}

// 新增地址
function onOpenAddAddr(type: any = "modal") {
  addAddressDrawerRef.value?.onShowDrawer(type);
}

// 编辑地址
function onOpenEditAddr(type: any = "modal", address: any) {
  addAddressDrawerRef.value?.onShowDrawer(type, address);
}

function onUpdateListUserAddress(type?: any) {
  if (type === "inline") {
    onListUserAddress("init");
  } else {
    onListUserAddress();
  }
}

const onCheckout = async () => {
  if (isEmptyObject(pageData?.orderAddress)) {
    showToast(authStore.i18n("cm_addr.pleaseSelectAddress"));
    return;
  }

  try {
    const selectedSkuList = <any>[];
    const currentGoodsList = props.cartData?.goodsList || [];

    currentGoodsList.forEach((goods: any) => {
      goods.skuList.forEach((sku: any) => {
        if (sku.selected) {
          selectedSkuList.push({
            quantity: sku.buyQty,
            skuId: sku.skuId,
            spm: sku.spm,
            routeId: goods.routeId,
            padc: sku.padc,
          });
        }
      });
    });

    if (selectedSkuList.length === 0) {
      showToast(authStore.i18n("cm_find.pleaseSelectGoods"));
      return;
    }

    // 调用快速创建订单接口
    const res: any = await useFastCreateOrder({
      skuList: selectedSkuList,
      siteId: window.siteData.siteInfo.id,
      orderAddress: pageData?.orderAddress,
    });

    if (res?.result?.code === 200) {
      navigateToPage(`/h5/order/details`, { orderNo: res.data.orderNo }, false);
    } else if (res?.result?.code === 403) {
      // 未登录，跳转到登录页面
      navigateToPage(
        `/h5/user/login`,
        { pageSource: window.location.href },
        false
      );
    } else {
      showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
    }
  } catch (error) {
    console.error("结算失败:", error);
    showToast(authStore.i18n("cm_find.errorMessage"));
  }
};

const onSaveCountry = () => {
  window.location.reload();
};

function openInquireTip() {
  pageData.showTipDrawer = !pageData.showTipDrawer;
}
</script>

<style scoped lang="scss">
:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
  border-color: #e50113;
}
</style>
