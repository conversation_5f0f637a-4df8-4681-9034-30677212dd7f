<template>
  <div class="quotation-required-tab">
    <!-- 商品列表区域 -->
    <div
      class="goods-section bg-white mx-[0.16rem] rounded-[0.16rem] p-[0.16rem] mb-[0.16rem]"
    >
      <div class="flex items-center justify-between mb-[0.16rem]">
        <div class="text-[0.32rem] font-medium text-[#1B80E4]">
          {{ authStore.i18n("cm_find.inquiryList") }}
        </div>
        <div class="text-[0.26rem] text-[#666]">
          ({{ cartData.stat?.skuCount || 0 }})
        </div>
      </div>

      <!-- 全选 -->
      <div class="flex items-center mb-[0.24rem]">
        <n-checkbox
          :checked="isSelectAll"
          @update:checked="onAllSelection"
          class="mr-[0.12rem]"
        />
        <span class="text-[0.28rem]">{{
          authStore.i18n("cm_find.selectAllItems")
        }}</span>
      </div>

      <!-- 商品列表 -->
      <div v-if="cartData?.goodsList?.length">
        <div
          v-for="(goods, index) in cartData.goodsList"
          :key="goods.goodsId"
          class="mb-[0.24rem]"
        >
          <!-- 商品信息 -->
          <div class="flex items-center mb-[0.16rem]">
            <n-checkbox
              v-model:checked="goods.selected"
              @update:checked="
                (value) => $emit('onGoodsSelection', value, goods)
              "
              class="mr-[0.12rem]"
            />
            <mobile-goods-card
              :goods="goods"
              class="flex-1"
              :spmIndex="index"
              spmCode="cart-goods-list"
            />
            <icon-card
              name="uil:trash-alt"
              color="#797979"
              size="0.4rem"
              @click="$emit('onDeleteGoods', goods)"
              class="ml-[0.12rem]"
            />
          </div>

          <!-- SKU列表 -->
          <div
            v-for="sku in goods.skuList"
            :key="sku.skuId"
            class="ml-[0.48rem] mb-[0.16rem]"
          >
            <div class="flex items-center">
              <n-checkbox
                v-model:checked="sku.selected"
                @update:checked="
                  (value) => $emit('onSkuSelection', value, sku, goods)
                "
                class="mr-[0.12rem]"
              />
              <mobile-sku-card
                :sku="sku"
                :goods="goods"
                @onCartQtyUpdate="
                  (value) => $emit('onCartQtyUpdate', value, sku, goods)
                "
                :step="sku.minIncreaseQuantity"
                class="flex-1"
              >
                <template v-slot:spec>
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="0.4rem"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  />
                </template>
                <template v-slot:delete>
                  <icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="0.36rem"
                    @click="$emit('onDeleteSku', sku, goods)"
                  />
                </template>
              </mobile-sku-card>
            </div>
          </div>

          <div
            v-if="index !== cartData.goodsList.length - 1"
            class="border-b border-[#F5F5F5] my-[0.16rem]"
          ></div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-[0.8rem]">
        <icon-card
          name="material-symbols:inbox-outline"
          color="#999"
          size="1.2rem"
          class="mx-auto mb-[0.16rem]"
        />
        <div class="text-[0.28rem] text-[#999]">
          {{ authStore.i18n("cm_find.noData") }}
        </div>
      </div>
    </div>

    <!-- 询价说明区域 -->
    <div
      class="inquiry-info bg-white mx-[0.16rem] rounded-[0.16rem] p-[0.16rem] mb-[0.16rem]"
    >
      <div class="flex items-start">
        <icon-card
          name="material-symbols:info-outline"
          color="#1B80E4"
          size="0.32rem"
          class="mr-[0.12rem] mt-[0.04rem]"
        />
        <div class="flex-1">
          <div class="text-[0.28rem] text-[#666] leading-[0.4rem]">
            {{ authStore.i18n("cm_find.inquiryDescription") }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <div
      class="fixed bottom-[1.32rem] left-0 right-0 bg-white border-t border-[#F5F5F5] p-[0.16rem]"
    >
      <div class="flex items-center justify-between mb-[0.16rem]">
        <div class="text-[0.28rem] text-[#666]">
          {{ authStore.i18n("cm_find.selectedItems") }}:
          {{ cartData.stat?.selectSkuCount || 0 }}
        </div>
        <div class="text-[0.32rem] font-medium text-[#E50113]">
          {{ formatPrice(cartData.stat?.selectTotalSalePrice || 0) }}
        </div>
      </div>

      <n-button
        block
        size="large"
        color="#E50113"
        text-color="#fff"
        class="rounded-[0.16rem] h-[0.88rem]"
        @click="onInquiry"
        :disabled="!cartData.stat?.selectSkuCount"
      >
        <div class="text-center">
          <div class="text-[0.32rem] font-medium">
            {{ authStore.i18n("cm_find.inquireNow") }}
          </div>
          <div class="text-[0.26rem] opacity-90">
            {{ authStore.i18n("cm_find.confirmWithoutPay") }}
          </div>
        </div>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import MobileGoodsCard from "./MobileGoodsCard.vue";
import MobileSkuCard from "./MobileSkuCard.vue";

const authStore = useAuthStore();

interface Props {
  cartData: any;
}

const props = defineProps<Props>();

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
  "onInquiry",
]);

// 计算属性
const isSelectAll = computed(
  () =>
    props.cartData?.goodsList?.every((goods: any) => goods.selected) || false
);

// 方法
const formatPrice = (price: number) => {
  return `US$ ${price.toFixed(2)}`;
};

const onAllSelection = (value: boolean) => {
  emit("onAllSelection", value, false);
};

const onInquiry = () => {
  emit("onInquiry");
};
</script>

<style scoped lang="scss">
.quotation-required-tab {
  padding-bottom: 2rem;
}

:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
  border-color: #e50113;
}
</style>
